package com.tencent.supersonic.chat.server.service;

import com.tencent.supersonic.chat.api.pojo.request.DashboardFilter;
import com.tencent.supersonic.chat.api.pojo.response.DashboardResp;
import com.tencent.supersonic.chat.server.dashboard.dataobject.DashboardDO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DashboardService {

    Long createDashboard(DashboardDO request);

    Long updateDashboard(DashboardDO request);

    void deleteDashboard(String id);

    List<DashboardResp> getAllDashboard(DashboardFilter filter);
}
