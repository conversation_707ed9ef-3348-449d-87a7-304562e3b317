package com.ultravis.bi.semantic.rewrite.adapters.oracle;

import org.apache.calcite.avatica.util.TimeUnit;
import org.apache.calcite.sql.*;
import org.apache.calcite.sql.dialect.OracleSqlDialect;

public class UltravisOracleSqlDialect extends OracleSqlDialect {

    public static final UltravisOracleSqlDialect DEFAULT = new UltravisOracleSqlDialect(OracleSqlDialect.DEFAULT_CONTEXT);

    public UltravisOracleSqlDialect(Context context) {
        super(context);
    }

    @Override
    public void unparseCall(SqlWriter writer, SqlCall call, int leftPrec, int rightPrec) {
        SqlOperator operator = call.getOperator();

        if (operator.getName().equalsIgnoreCase("TIMESTAMPADD")) {
            unparseTimestampAdd(writer, call);
        } else if (operator.getName().equalsIgnoreCase("TIMESTAMPDIFF")) {
            unparseTimestampDiff(writer, call);
        } else {
            super.unparseCall(writer, call, leftPrec, rightPrec);
        }
    }

    /**
     * Extracts TimeUnit from either SqlLiteral or SqlIntervalQualifier.
     * This handles both test cases (which use SqlLiteral) and actual parsing (which uses SqlIntervalQualifier).
     */
    private TimeUnit extractTimeUnit(SqlNode unitNode) {
        if (unitNode instanceof SqlLiteral unitLiteral) {
            // Handle test cases and some scenarios where unit is passed as SqlLiteral
            return unitLiteral.getValueAs(TimeUnit.class);
        } else if (unitNode instanceof SqlIntervalQualifier intervalQualifier) {
            // Handle actual parsing scenarios where unit is SqlIntervalQualifier
            return intervalQualifier.getUnit();
        } else {
            throw new IllegalArgumentException("Unsupported unit node type: " + unitNode.getClass().getSimpleName());
        }
    }

    private void unparseTimestampAdd(SqlWriter writer, SqlCall call) {
        SqlNode unitNode = call.operand(0);
        SqlNode intervalNode = call.operand(1);
        SqlNode tsNode = call.operand(2);

        TimeUnit unit = extractTimeUnit(unitNode);

        switch (unit) {
            case MONTH:
                writer.print("ADD_MONTHS(");
                tsNode.unparse(writer, 0, 0);
                writer.print(", ");
                intervalNode.unparse(writer, 0, 0);
                writer.print(")");
                break;
            case YEAR:
                writer.print("ADD_MONTHS(");
                tsNode.unparse(writer, 0, 0);
                writer.print(", ");
                writer.print("(");
                intervalNode.unparse(writer, 0, 0);
                writer.print(" * 12))");
                break;
            default:
                tsNode.unparse(writer, 0, 0);
                writer.print(" + INTERVAL '");
                intervalNode.unparse(writer, 0, 0);
                writer.print("' ");
                writer.print(unit.name());  // eg. DAY, HOUR
                writer.print(" ");  // Add space after time unit
                break;
        }
    }


    private void unparseTimestampDiff(SqlWriter writer, SqlCall call) {
        SqlNode unitNode = call.operand(0);
        SqlNode ts1 = call.operand(1);
        SqlNode ts2 = call.operand(2);

        TimeUnit unit = extractTimeUnit(unitNode);

        switch (unit) {
            case SECOND:
                writer.print("(");
                ts2.unparse(writer, 0, 0);
                writer.print(" - ");
                ts1.unparse(writer, 0, 0);
                writer.print(") * 86400");
                break;
            case MINUTE:
                writer.print("(");
                ts2.unparse(writer, 0, 0);
                writer.print(" - ");
                ts1.unparse(writer, 0, 0);
                writer.print(") * 1440");
                break;
            case HOUR:
                writer.print("(");
                ts2.unparse(writer, 0, 0);
                writer.print(" - ");
                ts1.unparse(writer, 0, 0);
                writer.print(") * 24");
                break;
            case DAY:
                writer.print("(");
                ts2.unparse(writer, 0, 0);
                writer.print(" - ");
                ts1.unparse(writer, 0, 0);
                writer.print(")");
                break;
            case MONTH:
                writer.print("MONTHS_BETWEEN(");
                ts2.unparse(writer, 0, 0);
                writer.print(", ");
                ts1.unparse(writer, 0, 0);
                writer.print(")");
                break;
            case YEAR:
                writer.print("FLOOR(MONTHS_BETWEEN(");
                ts2.unparse(writer, 0, 0);
                writer.print(", ");
                ts1.unparse(writer, 0, 0);
                writer.print(") / 12)");
                break;
            default:
                throw new IllegalArgumentException("Unsupported unit: " + unit);
        }
    }
}

