package com.ultravis.bi.infra.schema;

import com.tencent.supersonic.common.pojo.enums.DataTypeEnums;
import com.tencent.supersonic.headless.api.pojo.Field;
import com.tencent.supersonic.headless.api.pojo.Measure;
import com.tencent.supersonic.headless.api.pojo.ModelDetail;

import java.util.HashMap;
import java.util.Map;

final class MeasureInfo implements FieldInfo {
    private final Measure measure;
    private final ModelDetail modelDetail;
    private final Field physicalField;
    private final ColumnTypeToJavaTypeMapper dbTypeToJavaTypeMapper;

    MeasureInfo(Measure measure, ModelDetail modelDetail, ColumnTypeToJavaTypeMapper dbTypeToJavaTypeMapper) {
        this.measure = measure;
        this.modelDetail = modelDetail;
        this.dbTypeToJavaTypeMapper = dbTypeToJavaTypeMapper;

        this.physicalField = modelDetail.getFields().stream()
                .filter(f -> f.getFieldName().equals(measure.getFieldName()))
                .findFirst().orElse(null);
    }

    @Override
    public String getName() {
        return measure.getBizName();
    }

    @Override
    public Class<?> getType() {
        if (measure.getDataTypeEnum() != null) {
            DataTypeEnums dataTypeEnums = DataTypeEnums.of(measure.getDataTypeEnum());
            return Schemas.toJavaType(dataTypeEnums);
        }

        // TODO 根据物理字段类型和聚合函数类型推断字段类型
        Class<?> dataTypeClass = null;
        if (physicalField != null) {
            dataTypeClass = Schemas.dbTypeToJavaType(dbTypeToJavaTypeMapper, physicalField.getDataType());
        }
        if (dataTypeClass == null) {
            dataTypeClass = double.class;
        }

        String agg = measure.getAgg().toUpperCase();
        switch (agg) {
            case "SUM", "MAX", "MIN" -> {
                return dataTypeClass;
            }
            case "COUNT" -> {
                return int.class;
            }
        }

        return double.class;
    }

    @Override
    public String getSemanticType() {
        return "Measure";
    }

    @Override
    public String getExpr() {
        return measure.getExpr();
    }

    @Override
    public String getFormat() {
        return null;
    }

    @Override
    public String getDescription() {
        return physicalField == null
                ? measure.getName()
                : measure.getName() + "(" + getAgg() + ")";
    }

    @Override
    public String getAlias() {
        // TODO 暂不启用, 避免 与 DimFromMeasure 产生冲突
        return physicalField == null
                ? measure.getAlias()
                : null;
    }

    @Override
    public String getDisplayName() {
        return physicalField == null
                ? measure.getName()
                : measure.getName() + "(" + measure.getAgg() + ")";
    }

    @Override
    public Map<String, Object> getExt() {
        HashMap<String, Object> ext = new HashMap<>();
        if (measure.getConstraint() != null) {
            ext.put("filter", measure.getConstraint());
        }
        return ext;
    }

    @Override
    public boolean isCalculated() {
        return true;
    }

    @Override
    public String getAgg() {
        return measure.getAgg();
    }
}
