package com.ultravis.bi.infra.schema;

import com.tencent.supersonic.headless.api.pojo.Field;
import com.tencent.supersonic.headless.api.pojo.Measure;
import com.tencent.supersonic.headless.api.pojo.ModelDetail;
import com.tencent.supersonic.headless.api.pojo.response.ModelResp;

import java.util.Map;

final class DimFromMeasure implements FieldInfo {
    private final Measure measure;
    private final ModelResp modelResp;
    private final ColumnTypeToJavaTypeMapper dbTypeToJavaTypeMapper;
    private final ModelDetail modelDetail;
    private final Field physicalField;

    DimFromMeasure(Measure measure, ModelResp modelResp, ColumnTypeToJavaTypeMapper dbTypeToJavaTypeMapper) {
        this.measure = measure;
        this.modelResp = modelResp;
        this.modelDetail = modelResp.getModelDetail();
        this.dbTypeToJavaTypeMapper = dbTypeToJavaTypeMapper;

        this.physicalField = modelDetail.getFields().stream()
                .filter(f -> f.getFieldName().equals(measure.getFieldName()))
                .findFirst().orElse(null);
    }

    @Override
    public String getName() {
        return measure.getExpr();
    }

    @Override
    public String getExpr() {
        return measure.getExpr();
    }

    @Override
    public String getFormat() {
        return "";
    }

    @Override
    public String getDescription() {
        return measure.getName();
    }

    @Override
    public String getAlias() {
        return measure.getAlias();
    }

    @Override
    public String getDisplayName() {
        return measure.getName();
    }

    @Override
    public Map<String, Object> getExt() {
        return Map.of();
    }

    @Override
    public Class<?> getType() {
        return Schemas.dbTypeToJavaType(dbTypeToJavaTypeMapper, physicalField.getDataType());
    }

    @Override
    public String getSemanticType() {
        return "Dimension";
    }

    @Override
    public boolean isPhysicalExpression() {
        return true;
    }

    @Override
    public boolean isPhysicalField() {
        return true;
    }
}
