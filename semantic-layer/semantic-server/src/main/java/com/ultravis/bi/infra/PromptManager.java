package com.ultravis.bi.infra;

import com.langfuse.client.LangfuseClient;
import com.langfuse.client.resources.prompts.types.Prompt;
import com.tencent.supersonic.common.pojo.ModelRela;
import com.tencent.supersonic.headless.api.pojo.SchemaElement;
import com.tencent.supersonic.headless.api.pojo.SemanticSchema;
import com.tencent.supersonic.headless.api.pojo.response.ModelResp;
import com.ultravis.bi.infra.schema.Schemas;
import com.ultravis.bi.text2sql.ExampleQuery;
import dev.langchain4j.model.input.PromptTemplate;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static java.util.stream.Collectors.joining;

@Component
public class PromptManager {

    private final LangfuseClient langfuseClient;

    private static final String STANDARD_SQL_DATE_TIME_FUNCTIONS = """
            LOCALTIME -	Returns the current date and time in the session time zone in a value of datatype TIME
            LOCALTIME(precision) -	Returns the current date and time in the session time zone in a value of datatype TIME, with precision digits of precision
            LOCALTIMESTAMP - Returns the current date and time in the session time zone in a value of datatype TIMESTAMP
            LOCALTIMESTAMP(precision) -	Returns the current date and time in the session time zone in a value of datatype TIMESTAMP, with precision digits of precision
            CURRENT_TIME - Returns the current time in the session time zone, in a value of datatype TIMESTAMP WITH TIME ZONE
            CURRENT_DATE - Returns the current date in the session time zone, in a value of datatype DATE
            CURRENT_TIMESTAMP - Returns the current date and time in the session time zone, in a value of datatype TIMESTAMP WITH TIME ZONE
            EXTRACT(timeUnit FROM datetime) - Extracts and returns the value of a specified datetime field from a datetime value expression
            YEAR(date) - Equivalent to EXTRACT(YEAR FROM date). Returns an integer.
            QUARTER(date) - Equivalent to EXTRACT(QUARTER FROM date). Returns an integer between 1 and 4.
            MONTH(date) - Equivalent to EXTRACT(MONTH FROM date). Returns an integer between 1 and 12.
            WEEK(date) - Equivalent to EXTRACT(WEEK FROM date). Returns an integer between 1 and 53.
            DAYOFYEAR(date) - Equivalent to EXTRACT(DOY FROM date). Returns an integer between 1 and 366.
            DAYOFMONTH(date) - Equivalent to EXTRACT(DAY FROM date). Returns an integer between 1 and 31.
            DAYOFWEEK(date) - Equivalent to EXTRACT(DOW FROM date). Returns an integer between 1 and 7.
            HOUR(date) - Equivalent to EXTRACT(HOUR FROM date). Returns an integer between 0 and 23.
            MINUTE(date) - Equivalent to EXTRACT(MINUTE FROM date). Returns an integer between 0 and 59.
            SECOND(date) - Equivalent to EXTRACT(SECOND FROM date). Returns an integer between 0 and 59.
            TIMESTAMPADD(timeUnit, integer, datetime) - Returns datetime with an interval of (signed) integer timeUnits added. Equivalent to datetime + INTERVAL 'integer' timeUnit
            TIMESTAMPDIFF(timeUnit, datetime, datetime2) - Returns the (signed) number of timeUnit intervals between datetime and datetime2. Equivalent to (datetime2 - datetime) timeUnit
            LAST_DAY(date) - Returns the date of the last day of the month in a value of datatype DATE; For example, it returns DATE’2020-02-29’ for both DATE’2020-02-10’ and TIMESTAMP’2020-02-10 10:10:10’
            """;

    private final static String ORACLE_DATE_TIME_FUNCTIONS_OLD = """
            CURRENT_TIME - Returns the current time in the session time zone, in a value of datatype TIMESTAMP WITH TIME ZONE
            CURRENT_DATE - Returns the current date in the session time zone, in a value of datatype DATE
            CURRENT_TIMESTAMP - Returns the current date and time in the session time zone, in a value of datatype TIMESTAMP WITH TIME ZONE
            EXTRACT(timeUnit FROM datetime) - Extracts and returns the value of a specified datetime field from a datetime value expression
            datetime + INTERVAL 'integer' timeUnit - Returns datetime with an interval of (signed) integer timeUnits added.
            datetime - INTERVAL 'integer' timeUnit - Returns datetime with an interval of (signed) integer timeUnits subtracted.
            TO_DATE(string, format) - Converts a string to a date/time value based on the specified format.
            TO_TIMESTAMP(string, format) - Converts a string to a timestamp value based on the specified format.
            TO_CHAR(datetime, format) - Converts a date/time value to a string based on the specified format.
            LAST_DAY(date) - Returns the date of the last day of the month in a value of datatype DATE; For example, it returns DATE’2020-02-29’ for both DATE’2020-02-10’ and TIMESTAMP’2020-02-10 10:10:10’
            """;

    private final static String ORACLE_DATE_TIME_FUNCTIONS = """
            CURRENT_TIME - Returns the current time in the session time zone, in a value of datatype TIMESTAMP WITH TIME ZONE
            CURRENT_DATE - Returns the current date in the session time zone, in a value of datatype DATE
            CURRENT_TIMESTAMP - Returns the current date and time in the session time zone, in a value of datatype TIMESTAMP WITH TIME ZONE
            EXTRACT(timeUnit FROM datetime) - Extracts and returns the value of a specified datetime field from a datetime value expression
            TIMESTAMPADD(timeUnit, integer, datetime) - Returns datetime with an interval of (signed) integer timeUnits added. Equivalent to datetime + INTERVAL 'integer' timeUnit
            TIMESTAMPDIFF(timeUnit, datetime, datetime2) - Returns the (signed) number of timeUnit intervals between datetime and datetime2. Equivalent to (datetime2 - datetime) timeUnit
            TO_DATE(string, format) - Converts a string to a date/time value based on the specified format.
            TO_TIMESTAMP(string, format) - Converts a string to a timestamp value based on the specified format.
            TO_CHAR(datetime, format) - Converts a date/time value to a string based on the specified format.
            LAST_DAY(date) - Returns the date of the last day of the month in a value of datatype DATE; For example, it returns DATE’2020-02-29’ for both DATE’2020-02-10’ and TIMESTAMP’2020-02-10 10:10:10’
            
            """;

    private static final String PROMPT_TEXT_TO_SQL = """
            You are an expert in **AOSHI-SQL**, your task is to generate an **AOSHI-SQL** query to answer the user's question based on the provided schema and information.
            
            # **AOSHI-SQL**
            **AOSHI-SQL** is a semantic query language based on ANSI-SQL, extended with the following enhancements:
            - **Dimension** fields refer to columns or column expressions.
            - **Measure** fields abstract complex aggregation calculations and are referenced via the `AGGREGATE` function.
            - Support `<>` but not `!=`
            - Don't support applying '-' to arguments of type '<DATE> - <DATE>'.
            
            **Date/time functions**:
            {{dateTimeFunctions}}
            
            **Important Rules**:
            - Do not write nested aggregate expressions like `AGGREGATE(COUNT(...))` or `AGGREGATE(SUM(...))`.
            - Use `AGGREGATE(measureName)` **only** when `measureName` is defined as a Measure in the schema.
            - If no Measure is provided for a required calculation (e.g., counts), use standard SQL aggregate functions directly (e.g., `COUNT()`, `SUM()`) without wrapping them in `AGGREGATE`.
            - **Only use the `AGGREGATE` function to wrap predefined Measure fields. Never nest standard aggregate functions (e.g., COUNT, SUM) inside `AGGREGATE`.**
            - Strictly limit the Date/time functions to the ones listed above, do not use any other Date/time functions or syntax.
            - Prefer datetime functions over string functions when possible.
            
            **You must adhere to these rules without exception.**
            
            # Steps
            1. Analyze the user question to identify the main intent and required outputs.
            2. Match schema elements to the question, identifying all necessary Dimensions, Measures, tables, joins, and filters.
            3. Reason step-by-step through any logic, including relationships between tables, groupings, and required aggregations.
            4. Only once you have finished reasoning, produce a single well-formed **AOSHI-SQL** query as your answer.
            
            # Instructions
            - Carefully read the question, analyze the provided schema, and map the necessary data elements to create an accurate and efficient AOSHI-SQL query.
            - If the question is not answerable based on the provided schema, respond with empty string.
            - To facilitate future getById operations, include the most relevant primary key in the query result—provided it does not alter the query's intended semantics.
            - Strictly follow the provided schema—do not introduce any additional fields or tables, and ensure all fields used belong to their respective tables
            - Use fully qualified identifiers to resolve ambiguities.
            - Quote alias to avoid keywords collision.
            
            # Additional Instructions
            {{userInstructions}}
            
            -------
            
            # Schema Information
            {{schema}}
            
            # Example Queries
            {{exampleQueries}}
            
            # System Information
            * Current Date Time: {{currentDateTime}}
            
            ------
            
            User Question: {{question}}
            """;

    public PromptManager(LangfuseClient langfuseClient) {
        this.langfuseClient = langfuseClient;
    }

    @NotNull
    public static List<String> termsToInstructions(SemanticSchema semanticSchema) {
        return semanticSchema.getTerms().stream().map(PromptManager::termToInstruction).toList();
    }

    public static String termToInstruction(SchemaElement schemaElement) {
        if (schemaElement.getDescription() != null) {
            String names = Stream.concat(Stream.of(schemaElement.getName()), schemaElement.getAlias().stream())
                    .distinct()
                    .collect(joining(" | "));
            return "[Term Definition] " + names + ": " + schemaElement.getDescription();
        }
        return null;
    }

    /*
     * FIXME 暂时先用 hard code 的 prompt, 后面再接入 langfuse 等工具
     */
    public dev.langchain4j.model.input.Prompt textToSql(String question, Pair<List<ModelResp>, List<ModelRela>> modelsInfo, List<String> instructions, List<ExampleQuery> exampleQueries) {
        String schemaPrompt = Schemas.toContextStringCSV(modelsInfo.getLeft(), modelsInfo.getRight());
        Map<String, Object> variables = Map.of(
                "question", question,
                "schema", schemaPrompt,
                "userInstructions", instructionsToMarkdown(instructions),
                "exampleQueries", exampleQueriesToXml(exampleQueries),
                "currentDateTime", Instant.now().toString(),
                // TODO 根据数据库类型选择不同的日期函数
                "dateTimeFunctions", ORACLE_DATE_TIME_FUNCTIONS
        );
        return PromptTemplate.from(PROMPT_TEXT_TO_SQL).apply(variables);
    }

    /**
     * FIXME 暂时先用 hard code 的 prompt, 后面再接入 langfuse 等工具
     *
     * @param question
     * @param modelsInfo
     * @param instructions
     * @param exampleQueries
     * @return
     */
    public dev.langchain4j.model.input.Prompt textToSql_(String question, Pair<List<ModelResp>, List<ModelRela>> modelsInfo, List<String> instructions, List<ExampleQuery> exampleQueries) {
        Prompt prompt = langfuseClient.prompts().get("TextToSql/GenSql");
        String schemaPrompt = Schemas.toContextStringCSV(modelsInfo.getLeft(), modelsInfo.getRight());
        Map<String, Object> variables = Map.of(
                "question", question,
                "schema", schemaPrompt,
                "userInstructions", instructionsToMarkdown(instructions),
                "exampleQueries", exampleQueriesToXml(exampleQueries),
                "currentDateTime", Instant.now().toString());
        String promptText = prompt.getText().get().getPrompt();
        return PromptTemplate.from(promptText).apply(variables);
    }

    private String instructionsToMarkdown(List<String> instructions) {
        if (instructions == null || instructions.isEmpty()) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (String instruction : instructions) {
            sb.append("* ").append(instruction).append("\n");
        }
        return sb.toString();
    }

    /**
     * TODO 可能不适合用 markdown, 因为 SQL 中会包含 '||'
     *
     * @param exampleQueries
     * @return
     */
    private String exampleQueriesToMarkdown(List<ExampleQuery> exampleQueries) {
        if (exampleQueries == null || exampleQueries.isEmpty()) {
            return "";
        }

        // | name | sql | usageGuidance |
        // | --- | --- | --- |
        // | name1 | sql1 | usageGuidance1 |
        // | name2 | sql2 | usageGuidance2 |

        StringBuilder sb = new StringBuilder();
        sb.append("| Example query name | Sql | Usage Guidance |\n");
        sb.append("| --- | --- | --- |\n");
        for (ExampleQuery exampleQuery : exampleQueries) {
            sb.append("| ").append(exampleQuery.getName())
                    .append(" | ").append(newLineToSpace(exampleQuery.getSql()))
                    .append(" | ").append(nullToEmpty(exampleQuery.getUsageGuidance()))
                    .append(" |\n");
        }
        return sb.toString();
    }

    private String exampleQueriesToXml(List<ExampleQuery> exampleQueries) {
        if (exampleQueries == null || exampleQueries.isEmpty()) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        sb.append("<exampleQueries>\n");
        for (ExampleQuery exampleQuery : exampleQueries) {
            sb.append("<exampleQuery>\n");
            sb.append("name: ").append(exampleQuery.getName()).append("\n");
            sb.append("sql: ").append(newLineToSpace(exampleQuery.getSql())).append("\n");
            sb.append("usageGuidance: ").append(nullToEmpty(exampleQuery.getUsageGuidance())).append("\n");
            sb.append("</exampleQuery>\n");
        }
        sb.append("</exampleQueries>\n");
        return sb.toString();
    }

    @NotNull
    private static String newLineToSpace(String value) {
        return value.replace("\n", " ");
    }

    private static String nullToEmpty(String value) {
        return value != null ? value : "";
    }
}
