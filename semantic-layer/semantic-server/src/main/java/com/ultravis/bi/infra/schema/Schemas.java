package com.ultravis.bi.infra.schema;

import com.tencent.supersonic.common.pojo.ModelRela;
import com.tencent.supersonic.common.pojo.enums.DataTypeEnums;
import com.tencent.supersonic.common.pojo.enums.FilterOperatorEnum;
import com.tencent.supersonic.headless.api.pojo.Dimension;
import com.tencent.supersonic.headless.api.pojo.Identify;
import com.tencent.supersonic.headless.api.pojo.ModelDetail;
import com.tencent.supersonic.headless.api.pojo.SchemaElement;
import com.tencent.supersonic.headless.api.pojo.enums.DimensionType;
import com.tencent.supersonic.headless.api.pojo.response.ModelResp;
import com.ultravis.bi.semantic.SemanticMetadata;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticColumn;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.lang.reflect.Array;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class Schemas {

    public static final Pattern PATTERN_IDENTIFIER = Pattern.compile("[A-Za-z_][A-Za-z0-9_]*");

    @NotNull
    public static String getToDateExpr(String bizName, String dateFormat) {
        String[] args = Stream.concat(
                Stream.of(bizName),
                Arrays.stream(dateFormat.split("\\|")).map(it -> "'" + it + "'")
        ).toArray(String[]::new);
        String paramsFormat = String.join(", ", Collections.nCopies(args.length, "%s"));
        // TODO 根据数据库类型设置
        return String.format("TO_DATE(" + paramsFormat + ")", (Object[]) args);
        // return "TO_DATE(" + paramsFormat + ")".formatted((Object[]) args); //TODO 为什么不work?
    }

    // TODO 加入示例值(Value Examples)
    public static String toContextStringCSV(List<ModelResp> supersonicModels, List<ModelRela> relationships) {
        StringBuilder sb = new StringBuilder();
        sb.append("There are ").append(supersonicModels.size()).append(" tables:\n");

        sb.append("Table Name,Table Description\n");
        supersonicModels.forEach(model -> {
            String description = model.getDescription() != null ? model.getDescription() : model.getName();
            sb.append(model.getBizName()).append(",").append(description).append("\n");
        });
        sb.append("\n");

        supersonicModels.forEach(model -> {
            sb.append("The structure of table ").append(model.getBizName()).append(" is as follows:\n");
            sb.append("column name,column type,semantic type,column description\n");
            model.getModelDetail().getIdentifiers().forEach(identifier -> {
                String description = identifier.getName();
                if (description.contains(",")) {
                    description = "\"" + description + "\"";
                }
                sb.append(identifier.getBizName()).append(",").append("varchar").append(",")
                        .append("Primary key").append(",").append(description).append("\n");
            });
            model.getModelDetail().getDimensions().forEach(dim -> {
                String description = dim.getDescription() != null ? dim.getDescription() : dim.getName();
                if (description.contains(",")) {
                    description = "\"" + description + "\"";
                }
                String dataType = getColumnType(model.getModelDetail(), dim);
                sb.append(dim.getBizName()).append(",").append(dataType).append(",")
                        .append("Dimension").append(",").append(description).append("\n");
            });
            /// TODO 目前无脑把数值类型当做 Measure 是不正确的.
            model.getModelDetail().getMeasures().forEach(measure -> {
                String alias = measure.getAlias() != null ? measure.getAlias() : measure.getName();
                if (alias.contains(",")) {
                    alias = "\"" + alias + "\"";
                }
                // TODO 目前无脑把数值类型当做 Measure 是不正确的.
                String semanticType = "Dimension";
                sb.append(measure.getExpr()).append(",").append("number").append(",").append(semanticType).append(",")
                        .append(alias).append("\n");
            });
            model.getModelDetail().getMeasures().forEach(measure -> {
                String alias = measure.getAlias() != null ? measure.getAlias() : measure.getName();
                if (alias.contains(",")) {
                    alias = "\"" + alias + "\"";
                }
                // TODO 目前无脑把数值类型当做 Measure 是不正确的.
                String semanticType = "Measure";
                String bizName = measure.getBizName();
                alias += " " + measure.getAgg().toUpperCase();
                sb.append(bizName).append(",").append("number").append(",").append(semanticType).append(",")
                        .append(alias).append("\n");
            });
            sb.append("\n");
        });

        sb.append("\n");

        sb.append("The relationships between tables are as follows:\n");
        sb.append("from table,to table,join type,join conditions\n");
        relationships.forEach(rela -> {
            String fromModelName = getModelName(supersonicModels, rela.getFromModelId());
            String toModelName = getModelName(supersonicModels, rela.getToModelId());
            String str = getJoinConditionExpr(rela, fromModelName, toModelName);
            sb.append(fromModelName).append(",").append(toModelName).append(",").append(rela.getJoinType()).append(",")
                    .append(str).append("\n");
        });
        sb.append("\n");

        return sb.toString();
    }

    public static String toContextStringCSV2(SemanticMetadata md) {
        StringBuilder sb = new StringBuilder();
        sb.append("There are ").append(md.getModels().size()).append(" tables:\n");

        sb.append("Table Name,Table Description\n");
        md.getModels().forEach((modelName, model) -> {
            String description = model.getDescription() != null ? model.getDescription() : model.getName();
            sb.append(modelName).append(",").append(description).append("\n");
        });
        sb.append("\n");

        md.getModels().forEach((modelName, model) -> {
            sb.append("The structure of table ").append(modelName).append(" is as follows:\n");
            sb.append("column name,column type,semantic type,column description\n");

            // 主键
            model.getColumns().stream().filter(SemanticColumn::isPrimaryKey).forEach(col -> {
                String description = col.getDescription();
                if (description.contains(",")) {
                    description = "\"" + description + "\"";
                }
                sb.append(col.getName()).append(",").append(col.getType().getSimpleName()).append(",")
                        .append("Primary key").append(",").append(description).append("\n");
            });

            // 非主键
            model.getColumns().stream()
                    .filter(col -> !col.isPrimaryKey())
                    .forEach(dim -> {
                        String description = dim.getDescription() != null ? dim.getDescription() : dim.getName();
                        if (description.contains(",")) {
                            description = "\"" + description + "\"";
                        }
                        String dataType = dim.getType().getSimpleName();
                        sb.append(dim.getName()).append(",").append(dataType).append(",")
                                .append(dim.getSemanticType()).append(",").append(description).append("\n");
                    });
            sb.append("\n");
        });

        sb.append("\n");

        sb.append("The relationships between tables are as follows:\n");
        sb.append("from table,to table,join type,join conditions\n");

        md.getRelationships().forEach((name, rela) -> {
            sb.append(rela.fromModelName()).append(",").append(rela.toModelName()).append(",").append(rela.joinType()).append(",")
                    .append(rela.joinCondition()).append("\n");
        });
        sb.append("\n");

        return sb.toString();
    }

    public static String getJoinConditionExpr(ModelRela rela, String fromModelName, String toModelName) {
        return rela.getJoinConditions().stream()
                .map(cond -> {
                    String leftField = "%s.%s".formatted(fromModelName, cond.getLeftField());
                    String rightField = "%s.%s".formatted(toModelName, cond.getRightField());
                    return toJoinCondExpr(cond.getOperator(), leftField, rightField);
                })
                .collect(Collectors.joining(" AND "));
    }

    private static String toJoinCondExpr(FilterOperatorEnum operator, String leftField, String rightField) {
        return switch (operator) {
            case IN -> "%s IN (%s)".formatted(leftField, rightField);
            case NOT_IN -> "%s NOT IN (%s)".formatted(leftField, rightField);
            case EQUALS -> "%s = %s".formatted(leftField, rightField);
            case BETWEEN -> "%s BETWEEN %s".formatted(leftField, rightField);
            case GREATER_THAN -> "%s > %s".formatted(leftField, rightField);
            case GREATER_THAN_EQUALS -> "%s >= %s".formatted(leftField, rightField);
            case IS_NULL -> "%s IS NULL".formatted(leftField);
            case IS_NOT_NULL -> "%s IS NOT NULL".formatted(leftField);
            case LIKE -> "%s LIKE '%s'".formatted(leftField, rightField);
            case MINOR_THAN -> "%s < %s".formatted(leftField, rightField);
            case MINOR_THAN_EQUALS -> "%s <= %s".formatted(leftField, rightField);
            case NOT_EQUALS -> "%s <> %s".formatted(leftField, rightField);
            case SQL_PART -> leftField;
            case EXISTS -> "EXISTS(%s)".formatted(leftField);
        };
    }

    public static String getModelName(List<ModelResp> supersonicModels, Long modelId) {
        return supersonicModels.stream()
                .filter(m -> m.getId().equals(modelId))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("模型未找到: " + modelId)).getBizName();
    }

    private static String getColumnType(ModelDetail modelDetail, Dimension dim) {
        if (DimensionType.isTimeDimension(dim.getType())) {
            return "date";
        }
        return modelDetail.getFields().stream()
                .filter(f -> f.getFieldName().equals(dim.getFieldName()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("未找到对应的字段: " + dim.getName() + " -> " + dim.getFieldName()))
                .getDataType();
    }

    private static String getRawColumnType(ModelDetail modelDetail, Dimension dim) {
        return modelDetail.getFields().stream()
                .filter(f -> f.getFieldName().equals(dim.getFieldName()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("未找到对应的字段: " + dim.getName() + " -> " + dim.getFieldName()))
                .getDataType();
    }

    @Nullable
    public static Class<?> getJavaTypeOfPhysicalField(ColumnTypeToJavaTypeMapper dbTypeToJavaTypeMapper, ModelDetail modelDetail, Dimension dim) {
        return getJavaTypeOfPhysicalField(dbTypeToJavaTypeMapper, modelDetail, dim.getBizName(), dim.getFieldName());
    }

    public static Class<?> getJavaTypeOfPhysicalField(ColumnTypeToJavaTypeMapper dbTypeToJavaTypeMapper, ModelDetail modelDetail, Identify id) {
        return getJavaTypeOfPhysicalField(dbTypeToJavaTypeMapper, modelDetail, id.getBizName(), id.getFieldName());
    }

    @Nullable
    public static Class<?> getJavaTypeOfPhysicalField(ColumnTypeToJavaTypeMapper dbTypeToJavaTypeMapper, ModelDetail modelDetail, String dimName, String fieldName) {
        String dataType = modelDetail.getFields().stream()
                .filter(f -> f.getFieldName().equals(fieldName))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("未找到对应的字段: " + dimName + " -> " + fieldName))
                .getDataType();
        return dbTypeToJavaType(dbTypeToJavaTypeMapper, dataType);
    }

    @Nullable
    public static Class<?> dbTypeToJavaType(ColumnTypeToJavaTypeMapper dbTypeToJavaTypeMapper, String dataType) {
        Class<?> dataTypeClass;
        try {
            dataTypeClass = dbTypeToJavaTypeMapper.mapToJavaType(dataType);
        } catch (Exception e) {
//            log.warn("不支持的数据类型. Column Name: {}, Biz Name: {}, Data Type: {}. Caused By:", dim.getName(), dim.getBizName(), dataType, e);
            return null;
        }
        return dataTypeClass;
    }

    @NotNull
    public static String getQualifiedDimensionId(List<ModelResp> models, SchemaElement dim) {
        return getModelBizName(dim.getModel(), models) + "." + dim.getBizName();
    }

    public static String getModelBizName(Long modelId, List<ModelResp> key) {
        return getModelName(key, modelId);
    }


    public static Class<?> toJavaType(DataTypeEnums dataType) {
        return switch (dataType) {
            case INT -> int.class;
            case DOUBLE -> double.class;
            case DATE -> Date.class;
            case VARCHAR, UNKNOWN -> String.class;
            case BIGINT -> long.class;
            case FLOAT -> float.class;
            case DECIMAL -> BigDecimal.class;
            case ARRAY -> Array.class;
            case MAP, JSON -> Map.class;
        };
    }
}
