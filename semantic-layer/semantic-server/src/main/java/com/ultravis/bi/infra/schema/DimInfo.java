package com.ultravis.bi.infra.schema;

import com.tencent.supersonic.common.pojo.enums.DataTypeEnums;
import com.tencent.supersonic.headless.api.pojo.Field;
import com.tencent.supersonic.headless.api.pojo.ModelDetail;
import com.tencent.supersonic.headless.api.pojo.enums.DimensionType;
import com.tencent.supersonic.headless.api.pojo.response.DimSchemaResp;
import com.tencent.supersonic.headless.api.pojo.response.ModelResp;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
final class DimInfo implements FieldInfo {
    private final DimSchemaResp dim;
    private final ModelDetail modelDetail;
    private final Field physicalField;
    private final ColumnTypeToJavaTypeMapper dbTypeToJavaTypeMapper;
    private final ModelResp modelResp;

    DimInfo(DimSchemaResp dim, ModelResp modelResp, ColumnTypeToJavaTypeMapper dbTypeToJavaTypeMapper) {
        this.dim = dim;
        this.modelResp = modelResp;
        this.modelDetail = modelResp.getModelDetail();
        this.dbTypeToJavaTypeMapper = dbTypeToJavaTypeMapper;
        this.physicalField = modelDetail.getDimensions().stream()
                .filter(it -> it.getBizName().equals(dim.getBizName()))
                .findFirst()
                .flatMap(d -> modelDetail.getFields().stream()
                        .filter(f -> f.getFieldName().equalsIgnoreCase(d.getFieldName()))
                        .findFirst()).orElse(null);
    }

    @Override
    public String getName() {
        return dim.getBizName();
    }

    @Override
    public Class<?> getType() {
        if (physicalField != null) {
            return Schemas.dbTypeToJavaType(dbTypeToJavaTypeMapper, physicalField.getDataType());
        } else {
            // 从 扩展属性 中取出, 并转换类型
            DataTypeEnums dataType = dim.getDataType();
            if (dataType == null) {
                Object value = dim.getExt().get("dataTypeEnum");
                if (value != null) {
                    dataType = DataTypeEnums.of(String.valueOf(value));
                } else {
                    log.warn("维度 '{}.{}' 未指定数据类型.", modelResp.getBizName(), dim.getBizName());
                    dataType = DataTypeEnums.VARCHAR;
                }
            }
            return Schemas.toJavaType(dataType);
        }
    }

    @Override
    public String getSemanticType() {
        return "Dimension";
    }

    @Override
    public String getExpr() {
        return dim.getExpr();
    }

    @Override
    public String getFormat() {
        if (DimensionType.isTimeDimension(dim.getType())) {
            return (String) dim.getExt().get("time_format");
        }
        return null;
    }

    @Override
    public String getDescription() {
        return dim.getDescription();
    }

    @Override
    public String getAlias() {
        return dim.getAlias();
    }

    @Override
    public String getDisplayName() {
        return dim.getName();
    }

    @Override
    public Map<String, Object> getExt() {
        HashMap<String, Object> ext = new HashMap<>(dim.getExt());

        if (dim.getSensitiveLevel() != null) {
            ext.put("sensitiveLevel", dim.getSensitiveLevel());
        }
        if (dim.getModelFilterSql() != null) {
            ext.put("filter", dim.getModelFilterSql());
        }
        return ext;
    }

    @Override
    public boolean isPhysicalExpression() {
        return isPhysicalField()
                || dim.getExt().getOrDefault("isPhysicalExpression", true).equals(true);
    }

    @Override
    public boolean isPhysicalField() {
        return !isCalculated() && physicalField != null;
    }

    @Override
    public boolean isCalculated() {
        return !Schemas.PATTERN_IDENTIFIER.matcher(dim.getExpr()).matches();
    }

    public boolean isTimeDimension() {
        return DimensionType.isTimeDimension(dim.getType());
    }
}
