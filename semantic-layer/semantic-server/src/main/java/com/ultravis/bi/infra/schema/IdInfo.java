package com.ultravis.bi.infra.schema;

import com.tencent.supersonic.headless.api.pojo.Field;
import com.tencent.supersonic.headless.api.pojo.Identify;
import com.tencent.supersonic.headless.api.pojo.ModelDetail;
import com.tencent.supersonic.headless.api.pojo.response.ModelResp;

import java.util.Map;

final class IdInfo implements FieldInfo {
    private final Identify id;
    private final ModelDetail modelDetail;
    private final ColumnTypeToJavaTypeMapper dbTypeToJavaTypeMapper;
    private final ModelResp modelResp;

    IdInfo(Identify id, ModelResp modelResp, ColumnTypeToJavaTypeMapper dbTypeToJavaTypeMapper) {
        this.id = id;
        this.modelResp = modelResp;
        this.modelDetail = modelResp.getModelDetail();
        this.dbTypeToJavaTypeMapper = dbTypeToJavaTypeMapper;
    }

    @Override
    public String getName() {
        return id.getBizName();
    }

    @Override
    public Class<?> getType() {
        String dataType = modelDetail.getFields().stream()
                .filter(it -> it.getFieldName().equals(id.getBizName()))
                .findFirst().map(Field::getDataType)
                .orElseThrow();
        return Schemas.dbTypeToJavaType(dbTypeToJavaTypeMapper, dataType);
    }

    @Override
    public String getSemanticType() {
        return "Dimension";
    }

    @Override
    public String getExpr() {
        return id.getBizName();
    }

    @Override
    public String getFormat() {
        return null;
    }

    @Override
    public String getDescription() {
        return id.getName();
    }

    @Override
    public String getAlias() {
        return null;
    }

    @Override
    public String getDisplayName() {
        return id.getName();
    }

    @Override
    public Map<String, Object> getExt() {
        return Map.of(
                "isPrimaryKey", isPrimaryKey(),
                "isForeignKey", "foreign".equalsIgnoreCase(id.getType()),
                "isIdentifier", true
        );
    }

    @Override
    public boolean isPhysicalField() {
        return true;
    }

    @Override
    public boolean isPhysicalExpression() {
        return true;
    }

    @Override
    public boolean isIdentifier() {
        return true;
    }

    @Override
    public boolean isPrimaryKey() {
        return "primary".equalsIgnoreCase(id.getType());
    }
}
