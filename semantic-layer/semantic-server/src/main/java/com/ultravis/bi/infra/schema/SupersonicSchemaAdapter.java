package com.ultravis.bi.infra.schema;

import com.tencent.supersonic.headless.api.pojo.ModelDetail;
import com.tencent.supersonic.headless.api.pojo.response.MetricSchemaResp;
import com.tencent.supersonic.headless.api.pojo.response.ModelResp;
import com.tencent.supersonic.headless.api.pojo.response.SemanticSchemaResp;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticColumn;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticModel;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticRelationship;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.stream.Stream;

@Slf4j
public class SupersonicSchemaAdapter {
    private final SemanticSchemaResp superSemanticSchema;
    private final ColumnTypeToJavaTypeMapper dbTypeToJavaTypeMapper;

    public SupersonicSchemaAdapter(SemanticSchemaResp superSemanticSchema, ColumnTypeToJavaTypeMapper dbTypeToJavaTypeMapper) {
        this.superSemanticSchema = superSemanticSchema;
        this.dbTypeToJavaTypeMapper = dbTypeToJavaTypeMapper;
    }

    public List<SemanticModel> getSemanticModels() {
        return superSemanticSchema.getModelResps().stream().map(this::toSemanticModel).toList();
    }

    public List<SemanticRelationship> getSemanticRelationships() {
        List<SemanticRelationship> semanticRelationships = superSemanticSchema.getModelRelas()
                .stream().map(it -> {
                    String fromModelName = Schemas.getModelName(superSemanticSchema.getModelResps(), it.getFromModelId());
                    String toModelName = Schemas.getModelName(superSemanticSchema.getModelResps(), it.getToModelId());
                    return new SemanticRelationship(
                            fromModelName,
                            toModelName,
                            it.getJoinType(),
                            Schemas.getJoinConditionExpr(it, fromModelName, toModelName)
                    );
                }).toList();

        // TODO 暂时这么处理. 缺少图书借阅信息与基本信息的关联关系
        semanticRelationships = new ArrayList<>(semanticRelationships);
        semanticRelationships.add(new SemanticRelationship("kustBKSJSXX", "DWD_XSJBSJZLB", "inner join", "kustBKSJSXX.XH = DWD_XSJBSJZLB.XH"));
        semanticRelationships.add(new SemanticRelationship("DWD_XSJBSJZLB", "kustBKSJSXX", "inner join", "kustBKSJSXX.XH = DWD_XSJBSJZLB.XH"));

        return semanticRelationships;
    }

    private SemanticModel toSemanticModel(ModelResp supersonicModel) {
        SemanticModel model = new SemanticModel();
        model.setName(supersonicModel.getBizName());

        ModelDetail superPhysicalModel = supersonicModel.getModelDetail();

        if (superPhysicalModel.getQueryType().equals("table_query")) {
            model.setExpr(superPhysicalModel.getTableQuery());
        } else if (superPhysicalModel.getQueryType().equals("sql_query")) {
            model.setSql(superPhysicalModel.getSqlQuery());
        } else {
            throw new RuntimeException("Unsupported query type: " + superPhysicalModel.getQueryType());
        }

        // 主键和外键
        List<IdInfo> identifiers = superPhysicalModel.getIdentifiers().stream()
                //.filter(identifier -> identifier.getType().equals("primary"))
                .map(identifier -> new IdInfo(identifier, supersonicModel, dbTypeToJavaTypeMapper))
                .toList();

        // Dimensions
        List<DimInfo> dimInfos = superSemanticSchema.getDimensions().stream()
                .filter(dim -> dim.getModelId().equals(supersonicModel.getId()))
                .map(dim -> new DimInfo(dim, supersonicModel, dbTypeToJavaTypeMapper))
                .toList();

        // TODO 目前把数值类型自动创建为 Measure, 是错误的, 暂时将错就错.
        List<DimFromMeasure> dimFromMeasure = superPhysicalModel.getMeasures().stream()
                .map(measure -> new DimFromMeasure(measure, supersonicModel, dbTypeToJavaTypeMapper))
                .toList();

        // Measures
        List<MeasureInfo> measureInfos = superPhysicalModel.getMeasures().stream()
                .map(measure -> new MeasureInfo(measure, superPhysicalModel, dbTypeToJavaTypeMapper))
                .toList();

        List<SemanticColumn> columnList = Stream.concat(
                        Stream.concat(identifiers.stream(), dimInfos.stream()),
                        Stream.concat(dimFromMeasure.stream(), measureInfos.stream())
                )
                .map(field -> {

                    // 对未配置完全的时间类型字段, 修正其类型和表达式

                    Class<?> javaDataTypeClass = field.getType();
                    if (javaDataTypeClass == null) {
                        log.warn("忽略字段'{}.{}', 数据类型不正确.", supersonicModel.getBizName(), field.getName());
                        return null;
                    }

                    boolean isCalculated = field.isCalculated();
                    String expr = field.getExpr();

                    if (field instanceof DimInfo dim
                            && dim.isTimeDimension()
                            && dim.getType() == String.class
                    ) {
                        javaDataTypeClass = Date.class;
                        // TODO  不处理对义层新增字段
                        if (!isCalculated && dim.isPhysicalField()) {
                            isCalculated = true;
                            expr = getToDateExpr(field.getExpr(), field.getFormat());
                        }
                    }

                    return SemanticColumn.builder()
                            .name(field.getName())
                            .semanticType(field.getSemanticType())
                            .type(javaDataTypeClass)
                            .expr(expr)
                            .agg(field.getAgg())
                            .format(field.getFormat())
                            .semanticModel(model)
                            .calculated(isCalculated)
                            .isPhysicalExpression(field.isPhysicalExpression())
                            .description(field.getDescription())
                            .ext(field.getExt())
                            .primaryKey(field.isPrimaryKey())
                            .build();
                })
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        // TODO Metrics 转为 View
        // TODO 正确处理 Metrics 表达式
        Stream<MetricSchemaResp> metricSchemaRespStream = superSemanticSchema.getMetrics().stream()
                .filter(metric -> metric.getModelId().equals(supersonicModel.getId()));

        model.setColumns(columnList);
        return model;
    }

    @NotNull
    private static String getToDateExpr(String bizName, String dateFormat) {
        String[] args = Stream.concat(
                Stream.of(bizName),
                Arrays.stream(dateFormat.split("\\|")).map(it -> "'" + it + "'")
        ).toArray(String[]::new);
        String paramsFormat = String.join(", ", Collections.nCopies(args.length, "%s"));
        // TODO 根据数据库类型设置
        return String.format("TO_DATE(" + paramsFormat + ")", (Object[]) args);
        // return "TO_DATE(" + paramsFormat + ")".formatted((Object[]) args); //TODO 为什么不work?
    }
}
