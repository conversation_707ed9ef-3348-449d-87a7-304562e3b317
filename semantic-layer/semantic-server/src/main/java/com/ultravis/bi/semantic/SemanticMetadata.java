package com.ultravis.bi.semantic;

import com.ultravis.bi.semantic.rewrite.rewriter.SemanticColumn;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticModel;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticRelationship;
import lombok.Getter;

import java.util.Map;
import java.util.Optional;

/**
 * 语义模型元数据. 把语义层常用的数据合并到一起, 便于使用.
 */
@Getter
public class SemanticMetadata {

    private final Map<String, SemanticModel> models;
    private final Map<String, SemanticRelationship> relationships;

    public SemanticMetadata(Map<String, SemanticModel> models, Map<String, SemanticRelationship> relationships) {
        this.models = models;
        this.relationships = relationships;
    }

    public Optional<SemanticColumn> getColumn(String modelName, String dimName) {
        return Optional.ofNullable(models.get(modelName))
                .orElseThrow(() -> new RuntimeException("未找到模型: " + modelName))
                .getColumns().stream()
                .filter(col -> col.getName().equalsIgnoreCase(dimName))
                .findFirst();
    }
}
