package com.ultravis.bi.semantic;

import com.ultravis.bi.semantic.rewrite.rewriter.ColumnInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Accessors(chain = true)
@Data
public class ViewMetaInfo {
    /**
     * 视图名称
     */
    String title;
    /**
     * 描述
     */
    String description;
    /**
     * 视图 key
     */
    String name;
    /**
     * 基础模型 key
     */
    String baseModelName;
    /**
     * 视图暴漏的字段信息
     */
    List<ColumnInfo> columns;
    /**
     * 是否主视图
     */
    boolean isPrimary;
    /**
     * 序号
     */
    int seq;
}
