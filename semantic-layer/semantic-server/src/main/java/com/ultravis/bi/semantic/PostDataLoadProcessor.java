package com.ultravis.bi.semantic;

import com.tencent.supersonic.headless.api.pojo.SemanticSchema;
import com.tencent.supersonic.headless.api.pojo.response.ModelResp;
import com.ultravis.bi.infra.SensitiveFieldsRepo;
import com.ultravis.bi.semantic.rewrite.rewriter.ColumnInfo;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticRelationship;
import com.ultravis.bi.text2sql.DimensionValueUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.*;

/**
 * 对加载后的数据进行后处理
 */
public class PostDataLoadProcessor {

    public static final String SUFFIX_SYNTHESIZED_DESCRIPTION = "_synthesized_description";

    public static void addDescriptionColumns(SemanticSchema semanticSchema, List<ModelResp> models, DataLoadResult result) {
        Map<String, Map<String, List<String>>> dimensionIdToValueMapping = DimensionValueUtil.getValueMappingsOfDimension(semanticSchema, models);

        List<ColumnInfo> dataColumns = result.getParseResult().getColumns();
        Map<String, Map<String, List<String>>> columnNameToValueMapping =
                getColumnNameToValueMapping(dataColumns, dimensionIdToValueMapping);

        // 插入描述性字段
        for (int i = dataColumns.size() - 1; i >= 0; i--) {
            ColumnInfo columnInfo = dataColumns.get(i);
            if (!columnInfo.isField() || !columnNameToValueMapping.containsKey(columnInfo.getName())) {
                continue;
            }
            ColumnInfo descriptionColumn = new ColumnInfo()
                    .setName(getDescriptionColumnName(columnInfo.getName()))
                    .setDescription(getDescriptionColumnDescription(columnInfo.getDescription()));
            dataColumns.add(i + 1, descriptionColumn);
        }

        result.getData().forEach(row -> {
            List<String> cols = row.keySet().stream().toList();
            cols.forEach(col -> {
                if (!columnNameToValueMapping.containsKey(col)) {
                    return;
                }
                Object cell = row.get(col);
                if (cell == null) {
                    return;
                }
                Map<String, List<String>> nativeValueToSemanticValues = columnNameToValueMapping.get(col);
                List<String> semanticValues = nativeValueToSemanticValues.get(cell.toString());
                if (semanticValues != null && !semanticValues.isEmpty()) {
                    row.put(getDescriptionColumnName(col), semanticValues.getFirst());
                }
            });
        });
    }

    public static void addColumnMetaInfo(SemanticMetadata md, DataLoadResult result) {
        result.getParseResult().getColumns().stream()
                .filter(ColumnInfo::isField)
                .forEach(col -> setColumnMetaInfo(col, md));
    }

    public static void filterSensitiveFields(DataLoadResult result) {
        if (result.getData().isEmpty()) {
            return;
        }

        List<ColumnInfo> columns = result.getParseResult().getColumns();

        List<String> presentedModels = columns.stream()
                .filter(ColumnInfo::isField)
                .map(ColumnInfo::getOriginModelName)
                .distinct()
                .toList();
        BiFunction<String, String, Optional<SensitiveField>> matcher = SensitiveFieldsRepo.createMatcher(presentedModels);

        Map<String, SensitiveField.ProtectionPolicy> columnToFilter = columns.stream()
                .map(col -> {
                    return matcher.apply(col.getOriginModelName(), col.getOriginFieldName())
                            .map(f -> Pair.of(col, f))
                            .orElse(Pair.of(col, null));
                })
                .filter(it1 -> it1.getRight() != null)
                .collect(toMap(it -> it.getLeft().getName(), it -> it.getRight().getProtectionPolicy()));

        result.getData().forEach(row -> {
            columnToFilter.forEach((colName, filter) -> {
                Object cellValue = row.get(colName);
                String filteredCellValue = filter.apply(cellValue);
                row.put(colName, filteredCellValue);
            });
        });
    }

    private record ExtraInfo(String baseModel,
                             Map<String, List<ColumnInfo>> primaryKeysGroup,
                             Map<String, String> requiredParams) {
    }

    public static void addRelatedViewQueryGuidance(ParseSqlContext parseSqlContext, DataLoadResult execSqlResult) {
        Set<String> visited = new HashSet<>();
        Queue<String> queue = new LinkedList<>(visited);
        Map<String, ExtraInfo> extraInfos = new HashMap<>();

        // 存放顶层节点
        List<ViewQueryGuidance> topNodes = new ArrayList<>();
        // 存放可设置 children 的节点
        Map<String, ViewQueryGuidance> parentNodes = new HashMap<>();
        List<ViewMetaInfo> viewMetaInfos = new ArrayList<>();

        List<SemanticRelationship> biDirectionalRelationships = getBiDirectionalRelationships(parseSqlContext);
        Map<String, Integer> modelRank = rankModelsByFrequency(biDirectionalRelationships);

        Map<String, List<ColumnInfo>> modelToPrimaryKeys = execSqlResult.getParseResult().getColumns().stream()
                .filter(ColumnInfo::isPrimaryKey)
                .collect(groupingBy(
                        ColumnInfo::getOriginModelName,
                        Collectors.mapping(Function.identity(), Collectors.toList())
                ));

        modelToPrimaryKeys.forEach((modelName, primaryKeys) -> {
            queue.add(modelName);
            visited.add(modelName);

            Map<String, String> requiredParams = primaryKeys.stream().collect(toMap(ColumnInfo::getOriginFieldName, ColumnInfo::getName));
            extraInfos.put(modelName, new ExtraInfo(
                    modelName,
                    Map.of(modelName, primaryKeys),
                    requiredParams));

            ViewManager.getView(modelName, parseSqlContext).ifPresent(viewInfo -> {
                viewMetaInfos.add(
                        new ViewMetaInfo()
                                .setTitle(viewInfo.description())
                                .setDescription(viewInfo.description())
                                .setName(modelName)
                                .setBaseModelName(modelName)
                                .setColumns(viewInfo.parseResult().getColumns())
                                .setPrimary(false)
                                .setSeq(modelRank.get(modelName)));
                topNodes.add(new ViewQueryGuidance()
                        .setName(viewInfo.name())
                        .setBaseModelName(viewInfo.baseModelName())
                        .setPrimaryKeysGroup(viewInfo.parseResult().primaryKeysGroup())
                        .setParamToDataColumnMapping(requiredParams));
            });
        });

        while (!queue.isEmpty()) {
            String modelName = queue.poll();

            var extraInfo = extraInfos.get(modelName);
            Map<String, String> requiredParams = extraInfo.requiredParams();

            List<ViewQueryGuidance> children = new ArrayList<>();

            biDirectionalRelationships.stream()
                    .filter(r -> r.fromModelName().equals(modelName))
                    .filter(r -> !visited.contains(r.toModelName()))
                    .forEach(rel -> {
                        Optional<ViewManager.ParsedView> viewOpt =
                                ViewManager.getView(rel.fromModelName(), rel.toModelName(), parseSqlContext);
                        if (viewOpt.isEmpty()) {
                            return;
                        }
                        ViewManager.ParsedView view = viewOpt.get();

                        ViewMetaInfo viewMetaInfo = new ViewMetaInfo()
                                .setTitle(view.description())
                                .setDescription(view.description())
                                .setName(view.name())
                                .setBaseModelName(view.baseModelName())
                                .setColumns(view.parseResult().getColumns())
                                .setPrimary(false)
                                .setSeq(modelRank.get(view.baseModelName()));

                        ViewQueryGuidance guidance = new ViewQueryGuidance()
                                .setName(view.name())
                                .setBaseModelName(rel.toModelName())
                                .setTitle(view.description())
                                .setDescription(view.description())
                                .setPrimaryKeysGroup(view.parseResult().primaryKeysGroup())
                                .setParamToDataColumnMapping(requiredParams);

                        children.add(guidance);
                        viewMetaInfos.add(viewMetaInfo);

                        parentNodes.put(guidance.baseModelName, guidance);
                        visited.add(guidance.baseModelName);
                        queue.add(guidance.baseModelName);

                        extraInfos.put(guidance.baseModelName, new ExtraInfo(
                                guidance.baseModelName,
                                guidance.primaryKeysGroup,
                                guidance.primaryKeysGroup.getOrDefault(guidance.baseModelName, List.of()).stream()
                                        .collect(toMap(ColumnInfo::getOriginFieldName, ColumnInfo::getName))));
                    });

            ViewQueryGuidance parentGuidance = parentNodes.get(modelName);
            if (parentGuidance != null) {
                parentGuidance.setChildren(children);
            } else {
                topNodes.addAll(children);
            }
        }

        if (!viewMetaInfos.isEmpty()) {
            viewMetaInfos.sort(Comparator.comparingInt(ViewMetaInfo::getSeq));
            viewMetaInfos.getFirst().setPrimary(true);
        }

        execSqlResult.setRelatedViewQueryGuidance(new RelatedViewQueryGuidance(viewMetaInfos, topNodes));
    }

    @NotNull
    private static Map<String, Integer> rankModelsByFrequency(List<SemanticRelationship> biDirectionalRelationships) {
        Map<String, Long> countMap = biDirectionalRelationships.stream()
                .collect(groupingBy(
                        SemanticRelationship::fromModelName,
                        counting()));

        AtomicInteger index = new AtomicInteger(0);
        return countMap.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .collect(toMap(
                        Map.Entry::getKey,
                        entry -> index.getAndIncrement(),
                        (e1, e2) -> e1,
                        LinkedHashMap::new));
    }

    private static List<SemanticRelationship> getBiDirectionalRelationships(ParseSqlContext parseSqlContext) {
        Map<String, SemanticRelationship> rel = parseSqlContext.semanticMetadata.getRelationships();
        Stream<SemanticRelationship> backRel = rel.values().stream().map(r -> {
            return new SemanticRelationship(r.toModelName(), r.fromModelName(), r.joinType(), r.joinCondition());
        });
        return Stream.concat(rel.values().stream(), backRel).toList();
    }

    private static void setColumnMetaInfo(ColumnInfo col, SemanticMetadata md) {
        md.getColumn(col.getOriginModelName(), col.getOriginFieldName())
                .ifPresentOrElse(column -> {
                    col.setDescription(column.getDescription());
                }, () -> {
                    throw new RuntimeException("字段未找到: " + col.getOriginField());
                });
    }

    // {字段名: {字典值: [别名 1, 别名 2, ...]}}
    @NotNull
    private static Map<String, Map<String, List<String>>> getColumnNameToValueMapping(List<ColumnInfo> dataColumns, Map<String, Map<String, List<String>>> dimensionIdToValueMapping) {
        Map<String, Map<String, List<String>>> columnNameToValueMapping = new HashMap<>();
        dataColumns.stream()
                .filter(ColumnInfo::isField)
                .forEach(it -> {
                    Map<String, List<String>> nativeValueToSemanticValues = dimensionIdToValueMapping.get(it.getOriginField());
                    if (nativeValueToSemanticValues != null) {
                        columnNameToValueMapping.put(it.getName(), nativeValueToSemanticValues);
                    }
                });
        return columnNameToValueMapping;
    }

    @NotNull
    private static String getDescriptionColumnName(String name) {
        return name + SUFFIX_SYNTHESIZED_DESCRIPTION;
    }

    private static String getDescriptionColumnDescription(String description) {
        return description + "(描述)";
    }
}
