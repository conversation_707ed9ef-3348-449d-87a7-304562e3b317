package com.ultravis.bi.infra.schema;

import java.util.Map;

sealed interface FieldInfo permits DimInfo, DimFromMeasure, IdInfo, MeasureInfo {
    String getName();

    String getExpr();

    String getFormat();

    String getDescription();

    String getAlias();

    String getDisplayName();

    Map<String, Object> getExt();

    Class<?> getType();

    String getSemanticType();

    default boolean isPhysicalField() {
        return false;
    }

    default boolean isPhysicalExpression() {
        return false;
    }

    default boolean isIdentifier() {
        return false;
    }

    default boolean isCalculated() {
        return false;
    }

    default String getAgg() {
        return null;
    }

    default boolean isPrimaryKey() {
        return false;
    }
}
